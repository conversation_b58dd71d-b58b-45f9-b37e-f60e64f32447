/* eslint-env jest */
import "@testing-library/jest-native/extend-expect";

// Mock react-native-url-polyfill
jest.mock("react-native-url-polyfill/auto", () => {});

// Mock Expo modules
jest.mock("expo-constants", () => ({
  expoConfig: {
    extra: {
      supabaseUrl: "mock-supabase-url",
      supabaseAnonKey: "mock-supabase-anon-key",
    },
  },
}));

jest.mock("expo-router", () => ({
  router: {
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    canGoBack: jest.fn(() => true),
  },
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    canGoBack: jest.fn(() => true),
  })),
  useLocalSearchParams: jest.fn(() => ({})),
  useSegments: jest.fn(() => []),
  usePathname: jest.fn(() => "/"),
}));

// Mock Expo Secure Store
let secureStore = {};

jest.mock("expo-secure-store", () => ({
  getItemAsync: jest.fn((key) => Promise.resolve(secureStore[key] || null)),
  setItemAsync: jest.fn((key, value) => {
    secureStore[key] = value;
    return Promise.resolve();
  }),
  deleteItemAsync: jest.fn((key) => {
    delete secureStore[key];
    return Promise.resolve();
  }),
}));

jest.mock("expo-location", () => ({
  requestForegroundPermissionsAsync: jest.fn(() =>
    Promise.resolve({ status: "granted" })
  ),
  getCurrentPositionAsync: jest.fn(() =>
    Promise.resolve({
      coords: {
        latitude: 28.6139,
        longitude: 77.209,
      },
    })
  ),
  reverseGeocodeAsync: jest.fn(() =>
    Promise.resolve([
      {
        postalCode: "110001",
        city: "New Delhi",
        region: "Delhi",
      },
    ])
  ),
}));

jest.mock("@react-native-google-signin/google-signin", () => ({
  GoogleSignin: {
    configure: jest.fn(),
    hasPlayServices: jest.fn(() => Promise.resolve(true)),
    signIn: jest.fn(() =>
      Promise.resolve({
        user: {
          id: "mock-user-id",
          name: "Mock User",
          email: "<EMAIL>",
        },
        idToken: "mock-id-token",
      })
    ),
    signOut: jest.fn(() => Promise.resolve()),
    isSignedIn: jest.fn(() => Promise.resolve(false)),
    getCurrentUser: jest.fn(() => Promise.resolve(null)),
  },
}));

// Mock Supabase client
const mockSupabase = {
  auth: {
    signInWithIdToken: jest.fn().mockResolvedValue({ data: null, error: null }),
    signOut: jest.fn().mockResolvedValue({ error: null }),
    getSession: jest
      .fn()
      .mockResolvedValue({ data: { session: null }, error: null }),
    onAuthStateChange: jest.fn(() => ({
      data: { subscription: { unsubscribe: jest.fn() } },
    })),
    getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
  },
  from: jest.fn(() => mockSupabase.from),
  select: jest.fn(() => mockSupabase.from),
  insert: jest.fn(() => mockSupabase.from),
  update: jest.fn(() => mockSupabase.from),
  delete: jest.fn(() => mockSupabase.from),
  eq: jest.fn(() => mockSupabase.from),
  ilike: jest.fn(() => mockSupabase.from),
  in: jest.fn(() => mockSupabase.from),
  order: jest.fn(() => mockSupabase.from),
  range: jest.fn(() => mockSupabase.from),
  single: jest.fn().mockResolvedValue({ data: null, error: null }),
  limit: jest.fn(() => mockSupabase.from),
  count: jest.fn().mockResolvedValue({ count: 0, error: null }),
  rpc: jest.fn().mockResolvedValue({ data: null, error: null }),
};

jest.mock("@supabase/supabase-js", () => ({
  createClient: jest.fn(() => mockSupabase),
}));

// Mock React Native modules
// jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper', () => {});

// Mock React Native Alert
jest.mock("react-native", () => ({
  Alert: {
    alert: jest.fn(),
  },
  Platform: {
    OS: "ios",
  },
  StyleSheet: {
    create: jest.fn((styles) => styles),
  },
  Dimensions: {
    get: jest.fn(() => ({
      width: 375,
      height: 812,
    })),
  },
  Text: "Text",
  View: "View",
  TouchableOpacity: "TouchableOpacity",
  Pressable: "Pressable",
  ActivityIndicator: "ActivityIndicator",
  TextInput: "TextInput",
  ScrollView: "ScrollView",
  Image: "Image",
  Linking: {
    openURL: jest.fn(),
  },
  AppState: {
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    currentState: "active",
  },
}));

jest.mock("@expo/vector-icons", () => ({
  Ionicons: "Ionicons",
  MaterialCommunityIcons: "MaterialCommunityIcons",
  FontAwesome: "FontAwesome",
  // Add other icon sets as needed
}));

// Mock safe area context
jest.mock("react-native-safe-area-context", () => ({
  SafeAreaProvider: ({ children }) => children,
  SafeAreaView: ({ children }) => children,
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}));

// Mock Async Storage
let asyncStorage = {};

jest.mock("@react-native-async-storage/async-storage", () => ({
  getItem: jest.fn((key) => Promise.resolve(asyncStorage[key] || null)),
  setItem: jest.fn((key, value) => {
    asyncStorage[key] = value;
    return Promise.resolve();
  }),
  removeItem: jest.fn((key) => {
    delete asyncStorage[key];
    return Promise.resolve();
  }),
  clear: jest.fn(() => {
    asyncStorage = {};
    return Promise.resolve();
  }),
}));

// Mock react-native-svg
jest.mock("react-native-svg", () => ({
  Svg: "Svg",
  Path: "Path",
  G: "G",
  Circle: "Circle",
  Rect: "Rect",
  Line: "Line",
  Polygon: "Polygon",
  Polyline: "Polyline",
  Ellipse: "Ellipse",
  Text: "Text",
  TSpan: "TSpan",
  Defs: "Defs",
  LinearGradient: "LinearGradient",
  Stop: "Stop",
  ClipPath: "ClipPath",
  Mask: "Mask",
  Use: "Use",
  Symbol: "Symbol",
  Marker: "Marker",
  Image: "Image",
  ForeignObject: "ForeignObject",
}));

// Mock lucide-react-native
jest.mock("lucide-react-native", () => ({
  ArrowLeft: "ArrowLeft",
  Heart: "Heart",
  Share2: "Share2",
  Phone: "Phone",
  MessageCircle: "MessageCircle",
  User: "User",
  ShoppingBag: "ShoppingBag",
  Star: "Star",
  MapPin: "MapPin",
  Package: "Package",
  MoreVertical: "MoreVertical",
}));

// Mock Discovery Service
jest.mock("@/src/services/discovery/DiscoveryService", () => ({
  DiscoveryService: {
    getBusinessProfiles: jest.fn(),
    getBusinessProfile: jest.fn(),
    getProducts: jest.fn(),
    // Add other methods as needed
  },
}));

jest.mock("@/src/hooks/useTheme", () => ({
  useTheme: () => ({
    colors: {
      primary: "#007BFF",
      background: "#FFFFFF",
      cardBackground: "#F0F0F0",
      border: "#CCCCCC",
      textPrimary: "#333333",
      textSecondary: "#666666",
      destructive: "#FF0000",
      // Add other colors as needed by your components
    },
  }),
}));

// Global test environment setup
global.__DEV__ = true;

// Reset all mocks before each test
beforeEach(() => {
  // Clear storage mocks
  asyncStorage = {};
  secureStore = {};

  // Reset Supabase mock
  Object.keys(mockSupabase).forEach((key) => {
    if (
      typeof mockSupabase[key] === "function" &&
      mockSupabase[key].mockClear
    ) {
      mockSupabase[key].mockClear();
    }
  });
  Object.keys(mockSupabase.auth).forEach((key) => {
    if (
      typeof mockSupabase.auth[key] === "function" &&
      mockSupabase.auth[key].mockClear
    ) {
      mockSupabase.auth[key].mockClear();
    }
  });

  // Restore default mock implementations
  mockSupabase.from.mockImplementation(() => mockSupabase.from);
  mockSupabase.auth.getSession.mockResolvedValue({
    data: { session: null },
    error: null,
  });
  mockSupabase.auth.getUser.mockResolvedValue({
    data: { user: null },
    error: null,
  });
  mockSupabase.single.mockResolvedValue({ data: null, error: null });
  mockSupabase.count.mockResolvedValue({ count: 0, error: null });
  mockSupabase.rpc.mockResolvedValue({ data: null, error: null });
});

// Silence console warnings during tests
const originalWarn = console.warn;
console.warn = (...args) => {
  if (
    typeof args[0] === "string" &&
    args[0].includes("Warning: ReactDOM.render is no longer supported")
  ) {
    return;
  }
  originalWarn.call(console, ...args);
};

// Mock timers for consistent testing
jest.useFakeTimers();

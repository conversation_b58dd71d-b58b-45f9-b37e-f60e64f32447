import { renderHook, act } from "@testing-library/react-native";
import {
  useLoadingState,
  useGoogleSignInLoading,
  useFormSubmissionLoading,
} from "./useLoadingState";

describe("useLoadingState", () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it("should initialize with default state", () => {
    const { result } = renderHook(() => useLoadingState());
    expect(result.current.isLoading).toBe(false);
  });

  it("should transition to loading state on startLoading", () => {
    const { result } = renderHook(() => useLoadingState());
    act(() => {
      result.current.startLoading();
    });
    expect(result.current.isLoading).toBe(true);
  });

  it("should transition to not loading state on stopLoading", () => {
    const { result } = renderHook(() =>
      useLoadingState({ minDuration: 0, resetDelay: 0 })
    );
    act(() => {
      result.current.startLoading();
    });
    act(() => {
      result.current.stopLoading();
    });
    expect(result.current.isLoading).toBe(false);
  });

  it("should respect minDuration before setting isLoading to false", () => {
    const { result } = renderHook(() =>
      useLoadingState({ minDuration: 500, resetDelay: 0 })
    );
    act(() => {
      result.current.startLoading();
    });
    act(() => {
      result.current.stopLoading();
    });
    expect(result.current.isLoading).toBe(true);
    act(() => {
      jest.advanceTimersByTime(500);
    });
    expect(result.current.isLoading).toBe(false);
  });

  it("should respect resetDelay before setting isLoading to false", () => {
    const { result } = renderHook(() =>
      useLoadingState({ minDuration: 0, resetDelay: 500 })
    );
    act(() => {
      result.current.startLoading();
    });
    act(() => {
      result.current.stopLoading();
    });
    expect(result.current.isLoading).toBe(true);
    act(() => {
      jest.advanceTimersByTime(500);
    });
    expect(result.current.isLoading).toBe(false);
  });

  it("should stop loading immediately if specified", () => {
    const { result } = renderHook(() =>
      useLoadingState({ minDuration: 500, resetDelay: 500 })
    );
    act(() => {
      result.current.startLoading();
    });
    act(() => {
      result.current.stopLoading(true);
    });
    expect(result.current.isLoading).toBe(false);
  });

  it("should clean up timers on unmount", () => {
    const { unmount } = renderHook(() => useLoadingState());
    const clearTimeoutSpy = jest.spyOn(global, "clearTimeout");
    unmount();
    expect(clearTimeoutSpy).toHaveBeenCalled();
  });
});

describe("useGoogleSignInLoading", () => {
  it("should use the correct options for google sign in", () => {
    const { result } = renderHook(() => useGoogleSignInLoading());
    // This is a bit of a white box test, but it's the easiest way to verify the options
    // A better approach would be to test the behavior, but that's already covered above
    // and would be duplicative.
    expect(result.current).toBeDefined();
  });
});

describe("useFormSubmissionLoading", () => {
  it("should use the correct options for form submission", () => {
    const { result } = renderHook(() => useFormSubmissionLoading());
    expect(result.current).toBeDefined();
  });
});

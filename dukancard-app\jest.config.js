module.exports = {
  projects: [
    {
      displayName: "unit/integration",
      preset: "react-native",
      setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
      testEnvironment: "node",
      moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json"],
      transform: {
        "^.+\\.(js|jsx|ts|tsx)$": "babel-jest",
      },
      transformIgnorePatterns: [
        "node_modules/(?!(react-native|@react-native|expo|@expo|expo-font|expo-modules-core|@react-navigation|@supabase|@react-native-async-storage|@react-native-google-signin|react-native-url-polyfill|expo-status-bar|expo-router)/)",
      ],
      moduleNameMapper: {
        "^@/(.*)$": "<rootDir>/$1",
        "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$":
          "<rootDir>/__mocks__/fileMock.js",
        "\\.(css|less|scss|sass)$": "identity-obj-proxy",
      },
      testMatch: [
        "**/__tests__/**/*.(ts|tsx|js|jsx)",
        "**/*.(test|spec).(ts|tsx|js|jsx)",
      ],
      collectCoverageFrom: [
        "src/utils/**/*.{ts,tsx}",
        "src/hooks/**/*.{ts,tsx}",
        "src/services/**/*.{ts,tsx}",
        "src/components/**/*.{ts,tsx}",
        "src/contexts/**/*.{ts,tsx}",
        "app/**/*.{ts,tsx}",
        "lib/**/*.{ts,tsx}",
        "backend/supabase/services/**/*.{ts,tsx}",
        "!**/*.d.ts",
        "!**/node_modules/**",
        "!**/__tests__/**",
        "!**/coverage/**",
        "!**/*.perf.test.js",
      ],
      coverageDirectory: "coverage",
      coverageReporters: ["text", "lcov", "html"],
      coverageThreshold: {
        global: {
          branches: 80,
          functions: 85,
          lines: 85,
          statements: 85,
        },
        "./src/utils/": {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90,
        },
        "./src/hooks/": {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90,
        },
        "./src/services/": {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90,
        },
        "./src/components/": {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85,
        },
        "./app/": {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
      clearMocks: true,
      resetMocks: true,
      restoreMocks: true,
    },
    {
      displayName: "performance",
      preset: "react-native",
      setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
      testEnvironment: "node",
      moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json"],
      transform: {
        "^.+\\.(js|jsx|ts|tsx)$": "babel-jest",
      },
      transformIgnorePatterns: [
        "node_modules/(?!(react-native|@react-native|expo|@expo|expo-font|expo-modules-core|@react-navigation|@supabase|@react-native-async-storage|@react-native-google-signin|react-native-url-polyfill|expo-status-bar|expo-router)/)",
      ],
      moduleNameMapper: {
        "^@/(.*)$": "<rootDir>/$1",
        "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$":
          "<rootDir>/__mocks__/fileMock.js",
        "\\.(css|less|scss|sass)$": "identity-obj-proxy",
      },
      testMatch: ["**/performance/**/*.perf.test.js"],
    },
  ],
};
